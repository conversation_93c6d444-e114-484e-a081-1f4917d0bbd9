<?php

namespace App\Console\Commands;

use App\Models\Trip;
use App\Models\User;
use App\Models\Rider;
use App\Models\TripLocation;
use App\Enums\Trips\TripStatus;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;

class TestSharedTripView extends Command
{
    protected $signature = 'test:shared-trip-view';
    protected $description = 'Test the shared trip view by making an HTTP request';

    public function handle()
    {
        $this->info('Testing shared trip view with HTTP request...');

        try {
            // Create a user and rider
            $user = User::factory()->create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
            ]);

            $rider = Rider::factory()->create(['user_id' => $user->id]);

            // Create a trip location
            $tripLocation = TripLocation::create([
                'departure_address' => 'Test Departure Address',
                'arrival_address' => 'Test Arrival Address',
                'departure_lat' => 32.8872,
                'departure_lng' => 13.1913,
                'arrival_lat' => 32.9042,
                'arrival_lng' => 13.1856,
                'polyline' => 'test_polyline_data',
            ]);

            // Create a trip without driver (waiting for driver confirmation)
            $trip = Trip::create([
                'rider_id' => $rider->id,
                'driver_id' => null, // No driver assigned yet
                'vehicle_id' => null, // No vehicle assigned yet
                'status' => TripStatus::waiting_for_driver_confirmation,
                'trip_location_id' => $tripLocation->id,
                'share_token' => Str::random(32),
                'estimated_departure_time' => now()->addHour(),
                'estimated_arrival_time' => now()->addHours(2),
                'distance' => 10.5,
                'pricing_breakdown' => json_encode([
                    'base_fare' => 5.00,
                    'per_km' => 1.50,
                    'distance' => 10.5,
                    'subtotal' => 20.75,
                    'total' => 20.75,
                    'currency' => 'LYD',
                ]),
            ]);

            $this->info("Created test trip with ID: {$trip->id}");

            // Generate a signed URL for the shared trip
            $signedUrl = URL::temporarySignedRoute(
                'trips.shared',
                now()->addHours(24),
                ['shareToken' => $trip->share_token]
            );

            $this->info("Testing URL: {$signedUrl}");

            // Make an HTTP request to test the view
            $response = Http::get($signedUrl);

            if ($response->successful()) {
                $this->info('✅ HTTP request successful (Status: ' . $response->status() . ')');
                
                $content = $response->body();
                
                // Check for expected content
                if (str_contains($content, 'Waiting for driver assignment')) {
                    $this->info('✅ Found "Waiting for driver assignment" message');
                } else {
                    $this->warn('⚠️  Did not find "Waiting for driver assignment" message');
                }

                if (str_contains($content, 'A driver will be assigned shortly')) {
                    $this->info('✅ Found "A driver will be assigned shortly" message');
                } else {
                    $this->warn('⚠️  Did not find "A driver will be assigned shortly" message');
                }

                if (str_contains($content, 'Vehicle will be assigned with driver')) {
                    $this->info('✅ Found "Vehicle will be assigned with driver" message');
                } else {
                    $this->warn('⚠️  Did not find "Vehicle will be assigned with driver" message');
                }

                if (str_contains($content, 'Test Departure Address')) {
                    $this->info('✅ Found departure address');
                } else {
                    $this->warn('⚠️  Did not find departure address');
                }

                if (str_contains($content, 'Test Arrival Address')) {
                    $this->info('✅ Found arrival address');
                } else {
                    $this->warn('⚠️  Did not find arrival address');
                }

                // Check that there are no PHP errors in the response
                if (str_contains($content, 'Call to a member function hasEnoughRatingsForAverage() on null')) {
                    $this->error('❌ Found the original error in the response!');
                } else {
                    $this->info('✅ No PHP errors found in the response');
                }

                $this->info('✅ View test completed successfully!');

            } else {
                $this->error('❌ HTTP request failed with status: ' . $response->status());
                $this->error('Response body: ' . $response->body());
            }

            // Clean up
            $trip->delete();
            $tripLocation->delete();
            $rider->delete();
            $user->delete();

            $this->info('Test data cleaned up.');

        } catch (\Exception $e) {
            $this->error('❌ Test failed with error: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
            return 1;
        }

        return 0;
    }
}
